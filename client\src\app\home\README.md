# Home Module Layout Pattern

This home module uses a layout component pattern that allows for easy addition of child routes while maintaining consistent header, footer, and theme setup.

## Structure

```
home/
├── home-layout/
│   ├── home-layout.component.ts    # Layout component with common setup
│   ├── home-layout.component.html  # Header + router-outlet + Footer
│   └── home-layout.component.scss  # Layout-specific styles
├── home.component.*                # Main home page component
├── about/                          # Example child component
│   └── about.component.*
├── home-routing.module.ts          # Routing configuration
├── home.module.ts                  # Module declarations
└── README.md                       # This file
```

## How It Works

### Layout Component (`HomeLayoutComponent`)
- Handles common setup like theme injection and PrimeNG configuration
- Gets `commonContent` from route data (resolved at parent level)
- Provides consistent header and footer for all child routes
- Uses `<router-outlet>` to render child components

### Child Components
- Focus only on their specific content
- Get their own content via `contentResolver` with specific slugs
- No need to handle header/footer or theme setup
- Automatically wrapped in the layout

## Adding New Child Routes

### 1. Create Your Component
```typescript
// your-page/your-page.component.ts
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ContentService } from '../../core/services/content-vendor.service';

@Component({
  selector: 'app-your-page',
  templateUrl: './your-page.component.html',
  styleUrl: './your-page.component.scss'
})
export class YourPageComponent {
  content!: any;

  constructor(
    private route: ActivatedRoute,
    private CMSservice: ContentService
  ) { }

  ngOnInit(): void {
    this.content = this.route.snapshot.data['content'];
    // Process your content here
  }
}
```

### 2. Add to Module
```typescript
// home.module.ts
import { YourPageComponent } from './your-page/your-page.component';

@NgModule({
  declarations: [
    HomeComponent,
    HomeLayoutComponent,
    YourPageComponent  // Add here
  ],
  // ...
})
```

### 3. Add Route
```typescript
// home-routing.module.ts
{
  path: 'your-page',
  component: YourPageComponent,
  resolve: {
    content: contentResolver,
  },
  data: {
    slug: 'your-page-slug',  // CMS slug for your content
  },
}
```

### 4. Create Template
```html
<!-- your-page/your-page.component.html -->
<section class="your-page-sec">
  <div class="your-page-body relative max-w-1200 w-full mx-auto px-4">
    <!-- Your content here -->
    <!-- Header and footer are automatically included -->
  </div>
</section>
```

## Benefits

1. **Consistent Layout**: All child routes automatically get header/footer
2. **Theme Management**: Theme is loaded once in the layout component
3. **Common Content**: Header gets `commonContent` automatically
4. **Easy Maintenance**: Layout changes affect all child routes
5. **Clean Separation**: Child components focus only on their content
6. **Lazy Loading Ready**: Can easily convert child routes to lazy-loaded modules

## Example Routes

- `/home` - Main home page (HomeComponent)
- `/home/<USER>
- `/home/<USER>
- `/home/<USER>

## Converting to Lazy Loading

To convert a child route to lazy loading:

1. Create a separate module for the feature
2. Update the route to use `loadChildren`
3. The layout will still wrap the lazy-loaded content

```typescript
{
  path: 'services',
  loadChildren: () => import('./services/services.module').then(m => m.ServicesModule)
}
```
