import { NgModule } from '@angular/core';
import { ExtraOptions, RouterModule, Routes } from '@angular/router';
import { contentResolver } from './core/content-resolver';

const routerOptions: ExtraOptions = {
  anchorScrolling: 'enabled',
  scrollPositionRestoration: 'top',
  enableTracing: true,
  useHash: true
};

const routes: Routes = [
  {
    path: 'home',
    // canActivate: [AuthGuard],
    loadChildren: () =>
      import('./home/<USER>').then((m) => m.HomeModule),
    resolve: {
      commonContent: contentResolver,
    },
    data: {
      slug: 'common',
    },
  },
  {
    path: "auth",
    loadChildren: () =>
      import("./session/session.module").then((mod) => mod.SessionModule),
    resolve: {
      commonContent: contentResolver,
    },
    data: {
      slug: 'common',
    },
  },
  { path: '', redirectTo: 'home', pathMatch: 'full' },
  { path: '**', redirectTo: 'home' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, routerOptions)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
